import { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Image as RNImage,
  Platform,
  Share,
  Modal,
  StatusBar,
} from "react-native";
import { type EnrichedActivity } from "getstream";
import { Heart, MessageCircle, Pin, Share2, X, Link } from "lucide-react-native";
import { Image } from "expo-image";
import { router } from "expo-router";
import * as Linking from "expo-linking";

// Function to convert URLs in text to clickable links
const renderTextWithLinks = (text: string, style: any) => {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  const parts = text.split(urlRegex);

  return parts.map((part, index) => {
    if (part.match(urlRegex)) {
      return (
        <Text
          key={index}
          style={[style, { color: "#3B82F6", textDecorationLine: "underline" }]}
          onPress={() => Linking.openURL(part)}
        >
          {part}
        </Text>
      );
    }
    return (
      <Text key={index} style={style}>
        {part}
      </Text>
    );
  });
};

// Image Viewer Modal Component
const ImageViewer = ({
  visible,
  imageUri,
  onClose,
}: {
  visible: boolean;
  imageUri: string;
  onClose: () => void;
}) => {
  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <StatusBar hidden />
      <View style={styles.imageViewerOverlay}>
        <TouchableOpacity
          style={styles.imageViewerCloseButton}
          onPress={onClose}
        >
          <X size={24} color="#fff" />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.imageViewerContainer}
          activeOpacity={1}
          onPress={onClose}
        >
          <Image
            source={{ uri: imageUri }}
            style={styles.fullScreenImage}
            contentFit="contain"
          />
        </TouchableOpacity>
      </View>
    </Modal>
  );
};

// Component for dynamic image sizing
const DynamicImage = ({
  uri,
  style,
  onPress,
}: {
  uri: string;
  style: any;
  onPress?: () => void;
}) => {
  const [imageHeight, setImageHeight] = useState<number>(200);
  const screenWidth = Dimensions.get("window").width;
  const imageWidth = screenWidth - 64; // Account for padding

  const handleImageLoad = () => {
    RNImage.getSize(
      uri,
      (width: number, height: number) => {
        const aspectRatio = width / height;
        const calculatedHeight = imageWidth / aspectRatio;
        // Set reasonable bounds for height
        const finalHeight = Math.min(Math.max(calculatedHeight, 150), 600);
        setImageHeight(finalHeight);
      },
      (error: any) => {
        console.log("Error getting image size:", error);
        setImageHeight(200); // Fallback height
      }
    );
  };

  const ImageComponent = (
    <Image
      source={{ uri }}
      style={[style, { height: imageHeight }]}
      onLoad={handleImageLoad}
      contentFit="cover"
    />
  );

  if (onPress) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
        {ImageComponent}
      </TouchableOpacity>
    );
  }

  return ImageComponent;
};

interface EnrichedActivityWithText extends EnrichedActivity {
  text?: string;
  message?: string;
  image?: string;
  isPinned?: boolean;
  attachments?: Array<{
    type: string;
    image_url?: string;
    asset_url?: string;
    custom?: Record<string, any>;
  }>;
  og?: {
    url?: string;
    title?: string;
    description?: string;
    images?: Array<{
      image?: string;
      url?: string;
    }>;
    site_name?: string;
    type?: string;
  };
  own_reactions?: {
    like?: any[];
  };
  reaction_counts?: {
    like?: number;
    comment?: number;
  };
}

interface FeedPostProps {
  activity: EnrichedActivityWithText;
  onLike?: (activityId: string) => void;
  onComment?: (activity: EnrichedActivityWithText) => void;
  onPress?: (activity: EnrichedActivityWithText) => void;
  showActions?: boolean;
  showShare?: boolean;
  moduleConfig?: any;
  variant?: "feed" | "detail";
}

export const FeedPost = ({
  activity,
  onLike,
  onComment,
  onPress,
  showActions = true,
  showShare = true,
  moduleConfig,
  variant = "feed",
}: FeedPostProps) => {
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [selectedImageUri, setSelectedImageUri] = useState<string>("");
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return date.toLocaleDateString();
  };

  const getActorName = (actor: any): string => {
    if (typeof actor === "string") {
      const parts = actor.split(":");
      return parts[parts.length - 1] || actor;
    }
    if (actor?.data?.name) return actor.data.name;
    if (actor?.data?.firstName && actor?.data?.lastName) {
      return `${actor.data.firstName} ${actor.data.lastName}`;
    }
    if (actor?.id) return `User ${actor.id.substring(0, 8)}`;
    return "Unknown User";
  };

  const getActorImage = (actor: any): string | null => {
    if (actor?.data?.image) return actor.data.image;
    if (actor?.data?.avatarUrl) return actor.data.avatarUrl;
    return null;
  };

  const getActivityContent = (activity: EnrichedActivityWithText): string => {
    if (activity.message) return activity.message;
    if (activity.text) return activity.text;
    if (typeof activity.object === "string") return activity.object;
    const obj = activity.object as any;
    if (obj?.text) return obj.text;
    if (obj?.content) return obj.content;
    return "";
  };

  const isCreator = (actor: any): boolean => {
    if (actor?.data?.role) {
      return actor.data.role === "creator" || actor.data.role === "moderator";
    }
    return false;
  };

  const handleShare = async () => {
    const url = `${process.env.EXPO_PUBLIC_APP_BASE_URL}/post/${activity.id}`;
    try {
      if (Platform.OS === "android") {
        await Share.share({
          message: url,
        });
      } else {
        await Share.share({
          url,
        });
      }
    } catch (error) {
      console.error("Error sharing:", error);
    }
  };

  const handleImagePress = (imageUri: string) => {
    setSelectedImageUri(imageUri);
    setImageViewerVisible(true);
  };

  const handleCloseImageViewer = () => {
    setImageViewerVisible(false);
    setSelectedImageUri("");
  };

  const handlePostPress = () => {
    if (onPress) {
      onPress(activity);
    } else if (moduleConfig) {
      router.push({
        pathname: "/post/[id]",
        params: {
          id: activity.id,
          moduleConfig: JSON.stringify(moduleConfig),
        },
      });
    }
  };

  const avatarSize = variant === "detail" ? 48 : 38;
  const avatarRadius = avatarSize / 2;
  const userNameSize = variant === "detail" ? 16 : 14;
  const postTextSize = variant === "detail" ? 16 : 14;
  const postTextLineHeight = variant === "detail" ? 24 : 20;

  const PostContent = (
    <View
      style={[styles.postCard, variant === "detail" && styles.postCardDetail]}
    >
      <View style={styles.postHeader}>
        <View style={styles.userInfo}>
          {getActorImage(activity.actor) ? (
            <Image
              source={{ uri: getActorImage(activity.actor)! }}
              style={[
                styles.avatar,
                {
                  width: avatarSize,
                  height: avatarSize,
                  borderRadius: avatarRadius,
                },
              ]}
            />
          ) : (
            <View
              style={[
                styles.avatarPlaceholder,
                {
                  width: avatarSize,
                  height: avatarSize,
                  borderRadius: avatarRadius,
                },
              ]}
            >
              <Text
                style={[
                  styles.avatarText,
                  { fontSize: variant === "detail" ? 20 : 16 },
                ]}
              >
                {getActorName(activity.actor)[0]?.toUpperCase() || "U"}
              </Text>
            </View>
          )}
          <View style={styles.userMeta}>
            <View style={styles.nameContainer}>
              <Text style={[styles.userName, { fontSize: userNameSize }]}>
                {getActorName(activity.actor)}
              </Text>
              {isCreator(activity.actor) && (
                <View style={styles.creatorBadge}>
                  <Text style={styles.creatorText}>Creator</Text>
                </View>
              )}
            </View>
            <Text
              style={[
                styles.timestamp,
                { fontSize: variant === "detail" ? 14 : 12 },
              ]}
            >
              {formatTime(activity.time)}
            </Text>
          </View>
        </View>
        <View style={styles.postHeaderActions}>
          {activity.isPinned && (
            <Pin size={20} color="#FACC15" fill="#FACC15" />
          )}
          {showShare && (
            <TouchableOpacity
              onPress={(e) => {
                e.stopPropagation();
                handleShare();
              }}
              style={styles.shareButton}
            >
              <Share2 size={16} color="#9A9A9A" />
            </TouchableOpacity>
          )}
        </View>
      </View>

      <View style={styles.postContent}>
        <Text
          style={[
            styles.postText,
            { fontSize: postTextSize, lineHeight: postTextLineHeight },
          ]}
        >
          {renderTextWithLinks(getActivityContent(activity), [
            styles.postText,
            { fontSize: postTextSize, lineHeight: postTextLineHeight },
          ])}
        </Text>

        {/* Display legacy image field */}
        {activity.image && (
          <DynamicImage
            uri={activity.image}
            style={styles.postImage}
            onPress={() => handleImagePress(activity.image!)}
          />
        )}

        {/* Display attachments */}
        {activity.attachments && activity.attachments.length > 0 && (
          <View style={styles.attachmentsContainer}>
            {activity.attachments.map((attachment, index) => (
              <View key={index} style={styles.attachmentItem}>
                {attachment.type === "image" && attachment.image_url && (
                  <DynamicImage
                    uri={attachment.image_url}
                    style={styles.postImage}
                    onPress={() => handleImagePress(attachment.image_url!)}
                  />
                )}
                {attachment.type === "file" && attachment.asset_url && (
                  <TouchableOpacity
                    style={styles.fileAttachment}
                    onPress={() => {
                      console.log("File attachment:", attachment.asset_url);
                    }}
                  >
                    <Text style={styles.fileAttachmentText}>📎 View File</Text>
                  </TouchableOpacity>
                )}
              </View>
            ))}
          </View>
        )}

        {/* Open Graph Preview */}
        {activity.og && (
          <TouchableOpacity
            style={styles.ogPreviewContainer}
            activeOpacity={0.8}
            onPress={() => {
              if (activity.og?.url) {
                Linking.openURL(activity.og.url);
              }
            }}
          >
            {activity.og.images && activity.og.images.length > 0 && activity.og.images[0].image && (
              <Image
                source={{ uri: activity.og.images[0].image }}
                style={styles.ogPreviewImage}
                contentFit="cover"
              />
            )}
            <View style={styles.ogPreviewContent}>
              {activity.og.title && (
                <Text style={styles.ogPreviewTitle} numberOfLines={2}>
                  {activity.og.title}
                </Text>
              )}
              {activity.og.description && (
                <Text style={styles.ogPreviewDescription} numberOfLines={2}>
                  {activity.og.description}
                </Text>
              )}
              {activity.og.url && (
                <View style={styles.ogPreviewUrlContainer}>
                  <Link size={12} color="#3B82F6" />
                  <Text style={styles.ogPreviewUrl}>
                    {new URL(activity.og.url).hostname}
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        )}
      </View>

      {showActions && (
        <View style={styles.postActions}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={(e) => {
              e.stopPropagation();
              onLike?.(activity.id);
            }}
          >
            <Heart
              size={20}
              color={activity.own_reactions?.like?.length ? "#EF5252" : "#fff"}
              fill={
                activity.own_reactions?.like?.length ? "#EF5252" : "transparent"
              }
            />
            <Text
              style={[
                styles.actionText,
                activity.own_reactions?.like?.length
                  ? styles.likedText
                  : undefined,
              ]}
            >
              {activity.reaction_counts?.like || 0}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={(e) => {
              e.stopPropagation();
              onComment?.(activity);
            }}
          >
            <MessageCircle size={20} color="#fff" />
            <Text style={styles.actionText}>
              {activity.reaction_counts?.comment || 0}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );

  const FinalContent = (
    <>
      {PostContent}
      <ImageViewer
        visible={imageViewerVisible}
        imageUri={selectedImageUri}
        onClose={handleCloseImageViewer}
      />
    </>
  );

  if (variant === "detail" || !onPress) {
    return FinalContent;
  }

  return (
    <TouchableOpacity onPress={handlePostPress} activeOpacity={0.8}>
      {FinalContent}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  postCard: {
    backgroundColor: "#171D23",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  postCardDetail: {
    marginBottom: 0,
    borderRadius: 0,
  },
  postHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  avatar: {
    // Dynamic size set in component
  },
  avatarPlaceholder: {
    backgroundColor: "#EF5252",
    justifyContent: "center",
    alignItems: "center",
  },
  avatarText: {
    color: "#fff",
    fontWeight: "600",
  },
  userMeta: {
    gap: 2,
  },
  nameContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  userName: {
    fontWeight: "700",
    color: "#fff",
  },
  creatorBadge: {
    backgroundColor: "#EF5252",
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 4,
  },
  creatorText: {
    fontSize: 10,
    fontWeight: "500",
    color: "#fff",
  },
  timestamp: {
    color: "#9A9A9A",
  },
  postHeaderActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  shareButton: {
    padding: 4,
  },
  postContent: {
    marginBottom: 16,
  },
  postText: {
    color: "#D9D9D9",
  },
  postImage: {
    borderRadius: 8,
    marginTop: 12,
  },
  attachmentsContainer: {
    marginTop: 12,
  },
  attachmentItem: {
    marginBottom: 8,
  },
  fileAttachment: {
    backgroundColor: "#333",
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  fileAttachmentText: {
    color: "#9A9A9A",
    fontSize: 14,
  },
  postActions: {
    flexDirection: "row",
    gap: 24,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: "#242424",
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  actionText: {
    fontSize: 16,
    fontWeight: "700",
    color: "rgba(255, 255, 255, 0.8)",
  },
  likedText: {
    color: "#EF5252",
  },
  // Image Viewer Styles
  imageViewerOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.95)",
    justifyContent: "center",
    alignItems: "center",
  },
  imageViewerCloseButton: {
    position: "absolute",
    top: 50,
    right: 20,
    zIndex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: 20,
    padding: 8,
  },
  imageViewerContainer: {
    flex: 1,
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  fullScreenImage: {
    width: "100%",
    height: "100%",
  },
  ogPreviewContainer: {
    marginTop: 12,
    borderWidth: 1,
    borderColor: "#333",
    borderRadius: 8,
    overflow: "hidden",
    backgroundColor: "#1A1A1A",
  },
  ogPreviewImage: {
    width: "100%",
    height: 180,
    backgroundColor: "#333",
  },
  ogPreviewContent: {
    padding: 12,
  },
  ogPreviewTitle: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
    marginBottom: 4,
    lineHeight: 20,
  },
  ogPreviewDescription: {
    color: "#9A9A9A",
    fontSize: 13,
    marginBottom: 8,
    lineHeight: 18,
  },
  ogPreviewUrlContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  ogPreviewUrl: {
    color: "#3B82F6",
    fontSize: 12,
  },
});
